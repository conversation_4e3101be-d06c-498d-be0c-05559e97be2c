import React, {memo, useMemo} from 'react';
import {SharedValue, useDerivedValue} from 'react-native-reanimated';
import {Canvas, Image, useImage, Group, <Skottie
      style={styles.flex1}
      source={LottieAnimationFile}
      autoPlay={true}
    />} from '@shopify/react-native-skia';
import {Theme} from '../../themes';
import {heightScreen, widthScreen} from '../../utils/Scale';
import FastImage from 'react-native-fast-image';
import {StyleSheet, View} from 'react-native';
import AnimatedLottieView from 'lottie-react-native';
import {moderateVerticalScale, scale} from 'react-native-size-matters';

interface RolePlayBackgroundProps {
  modalContent: RolePlayCard | null;
  trasnY: SharedValue<number>;
  opacity: SharedValue<number>;
}

export const RolePlayBackground: React.FC<RolePlayBackgroundProps> = memo(
  ({modalContent, trasnY, opacity}) => {
    const nameImage = useImage(Theme.images.rolePlayName);

    const characterImageSource = useMemo(
      () =>
        (Theme.images as any)[modalContent?.rlFigureCode || ''] ??
        Theme.images.rolePlayCharactorDefault,
      [modalContent?.rlFigureCode],
    );
    const characterImage = useImage(characterImageSource);

    const nameTransform = useDerivedValue(
      () => [{translateY: trasnY.value}],
      [trasnY],
    );

    const characterTransform = useDerivedValue(
      () => [
        {translateX: (115.51 / 375) * widthScreen},
        {translateY: (310.3 / 812) * heightScreen},
      ],
      [],
    );

    if (!nameImage || !characterImage) {
      return null;
    }

    return (
      <View style={styles.container}>
        <FastImage
          source={Theme.images.rolePlayBlur}
          style={styles.blur}
          resizeMode="stretch"
        />

        <FastImage
          source={Theme.images.bgRolePlay}
          style={styles.bg}
          resizeMode="stretch"
        />

        <Canvas style={styles.canvasName}>
          <Group transform={nameTransform}>
            <Image
              image={nameImage}
              x={0}
              y={0}
              width={widthScreen}
              height={(248.16 / 812) * heightScreen}
              fit="fill"
            />
          </Group>
        </Canvas>

        <Canvas style={styles.canvasCharactor}>
          <Group transform={characterTransform} opacity={opacity}>
            {/* <Image
              image={characterImage}
              x={0}
              y={0}
              width={(143.97 / 375) * widthScreen}
              height={(236.7 / 812) * heightScreen}
              fit="fill"
            /> */}
          </Group>
        </Canvas>
        <AnimatedLottieView
          source={require('../../../assets/lotties/cheppy_blinked.json')}
          style={styles.lottie}
          autoPlay
          speed={0.7}
        />
      </View>
    );
  },
);
const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
  },
  blur: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: -2,
  },
  bg: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  canvasName: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: -1,
  },
  canvasCharactor: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  lottie: {
    width: scale((143.97 / 375) * widthScreen),
    height: moderateVerticalScale((236.7 / 812) * heightScreen),
    position: 'absolute',
  },
});
